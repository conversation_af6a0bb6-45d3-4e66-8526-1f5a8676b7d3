/**
 * 🚀 思源笔记激活码管理后端
 * 
 * 功能特性:
 * - 激活码生成 (单个/批量)
 * - 激活码管理 (查看/搜索/过滤/状态更新)
 * - 七牛云同步 (上传/下载/测试连接)
 * - 配置管理 (动态修改七牛云配置)
 * - 数据加密 (AES-256-CBC + PBKDF2)
 * - 数据导出 (多种格式支持)
 * 
 * 启动方式:
 * node backend.js [port]
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const qiniu = require('qiniu');

// 导入配置和加密模块
const config = require('./qiniu-config');
const encryption = require('./encryption');

class LicenseBackend {
    constructor(port = 3000) {
        this.port = port;
        this.app = express();
        this.licensesFile = path.join(__dirname, 'license-data', 'licenses.json');
        this.licenses = this.loadLicenses();
        
        this.initQiniu();
        this.setupMiddleware();
        this.setupRoutes();
    }

    // 初始化七牛云
    initQiniu() {
        const qiniuConfig = config.getSDKConfig();
        qiniu.conf.ACCESS_KEY = qiniuConfig.accessKey;
        qiniu.conf.SECRET_KEY = qiniuConfig.secretKey;
        
        this.qiniuConfig = new qiniu.conf.Config();
        this.qiniuConfig.zone = qiniu.zone[qiniuConfig.region];
        this.qiniuConfig.useHttpsDomain = true;
        
        this.mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);
        this.bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
        this.config = qiniuConfig;
    }

    // 设置中间件
    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, 'public')));
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
            res.header('Access-Control-Allow-Headers', 'Content-Type');
            next();
        });
    }

    // 设置路由
    setupRoutes() {
        // 静态页面
        this.app.get('/', (req, res) => res.sendFile(path.join(__dirname, 'public', 'index.html')));
        
        // API路由
        this.app.get('/api/licenses', (req, res) => res.json({ success: true, licenses: this.licenses }));
        this.app.get('/api/config', (req, res) => res.json({ success: true, config: config.get() }));
        this.app.post('/api/config', (req, res) => this.updateConfig(req, res));
        this.app.get('/api/test-connection', (req, res) => this.testConnection(req, res));
        
        this.app.post('/api/generate', (req, res) => this.generateLicense(req, res));
        this.app.post('/api/batch-generate', (req, res) => this.batchGenerate(req, res));
        // 统一同步接口 - 模仿思源笔记
        this.app.post('/api/sync/performSync', (req, res) => this.performSync(req, res));

        // 保留原接口兼容性
        this.app.post('/api/upload-qiniu', (req, res) => this.uploadToQiniu(req, res));
        this.app.post('/api/download-qiniu', (req, res) => this.downloadFromQiniu(req, res));
        
        this.app.post('/api/mark-used/:id', (req, res) => this.markAsUsed(req, res));
        this.app.post('/api/mark-active/:id', (req, res) => this.markAsActive(req, res));
        this.app.delete('/api/license/:id', (req, res) => this.deleteLicense(req, res));
        this.app.post('/api/batch-update-status', (req, res) => this.batchUpdateStatus(req, res));
        this.app.post('/api/batch-delete', (req, res) => this.batchDelete(req, res));
        
        this.app.get('/api/export/:format', (req, res) => this.exportLicenses(req, res));
    }

    // 激活码目录
    get licenseDir() { return path.join(__dirname, 'license-data'); }

    // 加载所有激活码
    loadLicenses() {
        try {
            if (!fs.existsSync(this.licenseDir)) fs.mkdirSync(this.licenseDir, { recursive: true });
            return fs.readdirSync(this.licenseDir)
                .filter(f => f.endsWith('.json'))
                .map(f => JSON.parse(fs.readFileSync(path.join(this.licenseDir, f), 'utf8')))
                .filter(Boolean);
        } catch { return []; }
    }

    // 保存激活码
    saveLicense(license) {
        try {
            if (!fs.existsSync(this.licenseDir)) fs.mkdirSync(this.licenseDir, { recursive: true });
            fs.writeFileSync(path.join(this.licenseDir, `${license.code}.json`), JSON.stringify(license, null, 2));
        } catch {}
    }

    // 删除激活码文件
    deleteLicenseFile(code) {
        try { fs.unlinkSync(path.join(this.licenseDir, `${code}.json`)); } catch {}
    }

    // 生成激活码
    generateLicense(req, res) {
        try {
            const { userId, userName, licenseType, notes = '' } = req.body;
            if (!userId || !userName || !licenseType) return res.json({ success: false, error: '缺少必需参数' });

            const license = encryption.generateLicense(licenseType, userId, userName, notes);
            this.saveLicense(license);
            this.licenses.push(license);
            res.json({ success: true, license });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 批量生成激活码
    batchGenerate(req, res) {
        try {
            const { licenseType, count } = req.body;
            if (!licenseType || !count || count < 1 || count > 100) return res.json({ success: false, error: '参数错误' });

            const licenses = Array.from({ length: count }, (_, i) => {
                const license = encryption.generateLicense(licenseType, `batch_user_${i + 1}`, `批量用户_${i + 1}`, '批量生成');
                this.saveLicense(license);
                return license;
            });

            this.licenses.push(...licenses);
            res.json({ success: true, licenses });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 更新配置
    updateConfig(req, res) {
        try {
            const result = config.update(req.body);
            if (result.success) {
                this.initQiniu(); // 重新初始化七牛云
                res.json({ success: true, message: '配置更新成功，已重新初始化七牛云连接' });
            } else {
                res.json(result);
            }
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 测试七牛云连接
    async testConnection(req, res) {
        try {
            await new Promise((resolve, reject) => {
                this.bucketManager.listPrefix(this.config.bucket, { limit: 1 }, (err, respBody, respInfo) => {
                    if (err) reject(err);
                    else if (respInfo.statusCode === 200) resolve();
                    else reject(new Error(`HTTP ${respInfo.statusCode}`));
                });
            });
            res.json({ success: true, message: '七牛云连接测试成功' });
        } catch (error) {
            res.json({ success: false, error: `连接失败: ${error.message}` });
        }
    }

    // 上传到七牛云 - 去重上传
    async uploadToQiniu(req, res) {
        try {
            // 获取七牛云现有文件列表
            const bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
            const listResult = await new Promise((resolve, reject) => {
                bucketManager.listPrefix(this.config.bucket, { prefix: '', limit: 1000 }, (err, respBody) => {
                    if (err) reject(err);
                    else resolve(respBody);
                });
            });

            const existingFiles = new Set(listResult.items.map(item => item.key));
            const uploadToken = new qiniu.rs.PutPolicy({ scope: this.config.bucket }).uploadToken(this.mac);
            const formUploader = new qiniu.form_up.FormUploader(this.qiniuConfig);
            let uploaded = 0, skipped = 0;

            for (const license of this.licenses) {
                const fileName = `${license.code}.dat`;

                if (existingFiles.has(fileName)) {
                    skipped++;
                    continue;
                }

                const encryptedData = encryption.encryptSingleLicense(license);
                await new Promise((resolve, reject) => {
                    formUploader.put(uploadToken, fileName, JSON.stringify(encryptedData), null, (err, respBody, respInfo) => {
                        if (err) reject(err);
                        else if (respInfo.statusCode === 200) { uploaded++; resolve(); }
                        else reject(new Error(`上传 ${license.code} 失败: HTTP ${respInfo.statusCode}`));
                    });
                });
            }

            res.json({ success: true, message: `上传完成: ${uploaded}个新增, ${skipped}个跳过` });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 统一同步接口 - 模仿思源笔记
    async performSync(req, res) {
        try {
            const action = req.body?.action || req.body || 'sync'; // 默认双向同步

            let uploadResult = null, downloadResult = null;

            if (action === 'upload' || action === 'sync') {
                uploadResult = await this.uploadToQiniuInternal();
            }

            if (action === 'download' || action === 'sync') {
                downloadResult = await this.downloadFromQiniuInternal();
            }

            const messages = [];
            if (uploadResult) messages.push(`上传: ${uploadResult.uploaded}新增, ${uploadResult.skipped}跳过`);
            if (downloadResult) messages.push(`下载: ${downloadResult.downloaded}新增, ${downloadResult.skipped}跳过`);

            res.json({
                success: true,
                message: messages.join(' | ') || '无操作',
                total: this.licenses.length,
                action: action === 'sync' ? '双向同步' : action === 'upload' ? '上传' : '下载'
            });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 内部上传方法 - 返回结果不响应
    async uploadToQiniuInternal() {
        const bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
        const listResult = await new Promise((resolve, reject) => {
            bucketManager.listPrefix(this.config.bucket, { prefix: '', limit: 1000 }, (err, respBody) => {
                if (err) reject(err);
                else resolve(respBody);
            });
        });

        const existingFiles = new Set(listResult.items.map(item => item.key));
        const uploadToken = new qiniu.rs.PutPolicy({ scope: this.config.bucket }).uploadToken(this.mac);
        const formUploader = new qiniu.form_up.FormUploader(this.qiniuConfig);
        let uploaded = 0, skipped = 0;

        for (const license of this.licenses) {
            const fileName = `${license.code}.dat`;
            if (existingFiles.has(fileName)) {
                skipped++;
                continue;
            }

            const encryptedData = encryption.encryptSingleLicense(license);
            await new Promise((resolve, reject) => {
                formUploader.put(uploadToken, fileName, JSON.stringify(encryptedData), null, (err, respBody, respInfo) => {
                    if (err) reject(err);
                    else if (respInfo.statusCode === 200) { uploaded++; resolve(); }
                    else reject(new Error(`上传 ${license.code} 失败: HTTP ${respInfo.statusCode}`));
                });
            });
        }

        return { uploaded, skipped };
    }

    // 内部下载方法 - 返回结果不响应
    async downloadFromQiniuInternal() {
        const existingCodes = new Set(this.licenses.map(l => l.code));
        const bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
        const listResult = await new Promise((resolve, reject) => {
            bucketManager.listPrefix(this.config.bucket, { prefix: '', limit: 1000 }, (err, respBody) => {
                if (err) reject(err);
                else resolve(respBody);
            });
        });

        let downloaded = 0, skipped = 0;
        for (const item of listResult.items.filter(f => f.key.endsWith('.dat'))) {
            const code = item.key.replace('.dat', '');
            if (existingCodes.has(code)) {
                skipped++;
                continue;
            }

            try {
                const publicUrl = `${this.config.domain}/${item.key}`;
                const response = await fetch(publicUrl);
                if (response.ok) {
                    const encryptedData = await response.json();
                    const license = encryption.decryptSingleLicense(encryptedData);
                    this.licenses.push(license);
                    this.saveLicense(license);
                    downloaded++;
                }
            } catch (error) {
                console.warn(`下载 ${item.key} 失败:`, error.message);
            }
        }

        return { downloaded, skipped };
    }

    // 从七牛云下载 - 去重下载
    async downloadFromQiniu(req, res) {
        try {
            // 获取本地已有的激活码
            const existingCodes = new Set(this.licenses.map(l => l.code));

            // 获取七牛云文件列表
            const bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
            const listResult = await new Promise((resolve, reject) => {
                bucketManager.listPrefix(this.config.bucket, { prefix: '', limit: 1000 }, (err, respBody) => {
                    if (err) reject(err);
                    else resolve(respBody);
                });
            });

            let downloaded = 0, skipped = 0;
            for (const item of listResult.items.filter(f => f.key.endsWith('.dat'))) {
                const code = item.key.replace('.dat', '');

                if (existingCodes.has(code)) {
                    skipped++;
                    continue;
                }

                try {
                    const publicUrl = `${this.config.domain}/${item.key}`;
                    const response = await fetch(publicUrl);
                    if (response.ok) {
                        const encryptedData = await response.json();
                        const license = encryption.decryptSingleLicense(encryptedData);
                        this.licenses.push(license);
                        this.saveLicense(license);
                        downloaded++;
                    }
                } catch (error) {
                    console.warn(`下载 ${item.key} 失败:`, error.message);
                }
            }

            res.json({ success: true, message: `下载完成: ${downloaded}个新增, ${skipped}个跳过` });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 更新激活码状态
    updateLicenseStatus(req, res, status, message) {
        const license = this.licenses.find(l => l.userId === req.params.id);
        if (!license) return res.json({ success: false, error: '激活码不存在' });
        license.status = status;
        this.saveLicense(license);
        res.json({ success: true, message });
    }

    // 标记为已使用
    markAsUsed(req, res) { this.updateLicenseStatus(req, res, 'used', '已标记为已使用'); }

    // 标记为有效
    markAsActive(req, res) { this.updateLicenseStatus(req, res, 'active', '已重新激活'); }

    // 删除激活码
    deleteLicense(req, res) {
        const index = this.licenses.findIndex(l => l.userId === req.params.id);
        if (index === -1) return res.json({ success: false, error: '激活码不存在' });
        const license = this.licenses.splice(index, 1)[0];
        this.deleteLicenseFile(license.code);
        res.json({ success: true, message: '激活码已删除' });
    }

    // 批量更新状态
    batchUpdateStatus(req, res) {
        const { ids, status } = req.body;
        const updated = ids.reduce((count, id) => {
            const license = this.licenses.find(l => l.userId === id);
            if (license) { license.status = status; this.saveLicense(license); return count + 1; }
            return count;
        }, 0);
        res.json({ success: true, message: `批量更新成功: ${updated}个` });
    }

    // 批量删除
    batchDelete(req, res) {
        const { ids } = req.body;
        this.licenses.filter(l => ids.includes(l.userId)).forEach(l => this.deleteLicenseFile(l.code));
        this.licenses = this.licenses.filter(l => !ids.includes(l.userId));
        res.json({ success: true, message: `批量删除成功: ${ids.length}个` });
    }

    // 导出激活码
    exportLicenses(req, res) {
        const { format } = req.params;
        const { type } = req.query;

        let licenses = this.licenses;
        if (type) {
            // 通过解析激活码获取类型信息进行过滤
            licenses = licenses.filter(l => {
                try {
                    const parsed = encryption.parseActivationCode(l.code);
                    return parsed.licenseType === type;
                } catch {
                    return false;
                }
            });
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `licenses_${timestamp}.${format === 'csv' ? 'csv' : 'txt'}`;

        let content = '';
        if (format === 'csv') {
            content = '激活码,用户ID,用户名,设备数,状态,备注,密钥\n';
            content += licenses.map(l =>
                `${l.code},${l.userId},${l.userName},${l.maxDevices},${l.status},${l.notes},${l.key.substring(0, 8)}...`
            ).join('\n');
        } else {
            content = licenses.map(l => l.code).join('\n');
        }

        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Type', 'text/plain; charset=utf-8');
        res.send(content);
    }

    // 启动服务器
    start() {
        this.app.listen(this.port, () => {
            console.log('🎯 思源笔记激活码管理中心 - 极简版');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`🚀 服务器启动成功: http://localhost:${this.port}`);
            console.log(`📊 当前激活码数量: ${this.licenses.length}`);
            console.log(`☁️  七牛云存储空间: ${this.config.bucket}`);
            console.log(`🔐 加密密钥: ${config.getRaw().encryptionKey.substring(0, 8)}...`);
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('💡 核心特性:');
            console.log('   • 🎲 24位激活码 - 完全随机，无规律可循');
            console.log('   • 🔑 随机密钥 - 每个激活码独立64位密钥');
            console.log('   • 📋 极简结构 - 7个字段，独立JSON文件');
            console.log('   • ☁️  七牛云同步 - 每个激活码独立加密压缩包');
            console.log('   • 🔄 批量操作 - 生成/管理/上传/下载');
            console.log('   • ✨ 优雅完美 - 极限精简，简洁高效');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        });
    }
}

// 启动服务器
const port = process.argv[2] || 3000;
const backend = new LicenseBackend(port);
backend.start();
