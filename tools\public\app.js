class LicenseApp {
    constructor() {
        this.licenses = [];
        this.init();
    }

    init() {
        this.setupEvents();
        this.loadData();
    }

    setupEvents() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => 
            btn.onclick = () => this.switchTab(btn.dataset.tab)
        );

        // 表单提交
        [
            ['generateForm', '/api/generate', 'generateResult'],
            ['batchGenerateForm', '/api/batch-generate', 'batchResult'],
            ['configForm', '/api/config', 'configResult']
        ].forEach(([id, url, resultId]) => {
            document.getElementById(id).onsubmit = (e) => {
                e.preventDefault();
                this.apiCall(url, Object.fromEntries(new FormData(e.target)), resultId, null, e.target);
            };
        });

        // 搜索过滤
        ['searchInput', 'typeFilter', 'timeFilter'].forEach(id => 
            document.getElementById(id).oninput = () => this.filterLicenses()
        );

        // 其他事件
        document.getElementById('groupByType').onchange = () => this.renderLicenses();
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.name === 'selectLicense') {
                this.updateBatchControls();
            }
        });
    }

    switchTab(tabId) {
        document.querySelectorAll('.tab-btn, .tab-content').forEach(el => el.classList.remove('active'));
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    async apiCall(url, data, resultId, statusId = null, form = null) {
        if (statusId) this.setStatus(statusId, '正在处理...');

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            const result = await response.json();

            if (result.success) {
                this.handleSuccess(result, resultId, statusId);
                if (form && url.includes('generate')) form.reset();
            } else {
                this.showMessage(resultId, 'error', `操作失败: ${result.error}`);
                if (statusId) this.setStatus(statusId, '操作失败', 'error');
            }
        } catch (error) {
            this.showMessage(resultId, 'error', `请求失败: ${error.message}`);
            if (statusId) this.setStatus(statusId, '请求异常', 'error');
        }
    }

    handleSuccess(result, resultId, statusId) {
        if (result.license) {
            this.licenses.push(result.license);
            this.showMessage(resultId, 'success', `生成成功: ${result.license.code}`);
        } else if (result.licenses) {
            this.licenses.push(...result.licenses);
            this.showMessage(resultId, 'success', `批量生成成功: ${result.licenses.length}个`);
        } else {
            this.showMessage(resultId, 'success', result.message || '操作成功');
        }
        if (statusId) this.setStatus(statusId, '操作成功', 'success');

        // 如果是配置更新，重新加载配置
        if (resultId === 'configResult') {
            setTimeout(() => this.loadConfig(), 1000);
        } else {
            this.refresh();
        }
    }

    // 连接测试
    testConnection() { this.apiCall('/api/test-connection', {}, 'testResult'); }

    // 数据加载
    async loadData() {
        await Promise.all([this.loadLicenses(), this.loadConfig()]);
    }

    async loadLicenses() {
        try {
            const response = await fetch('/api/licenses');
            const result = await response.json();
            if (result.success) {
                this.licenses = result.licenses;
                this.refresh();
            }
        } catch (error) {
            console.error('加载失败:', error);
        }
    }

    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const result = await response.json();
            if (result.success) {
                const config = result.config;

                // 填充配置表单
                document.getElementById('configAccessKey').value = config.accessKey || '';
                document.getElementById('configSecretKey').value = config.secretKey || '';
                document.getElementById('configBucket').value = config.bucket || '';
                document.getElementById('configRegion').value = config.region || '';
                document.getElementById('configDomain').value = config.domain || '';
                document.getElementById('configFileName').value = config.fileName || '';
                document.getElementById('configEncryptionKey').value = config.encryptionKey || '';

                this.showMessage('configResult', 'success', '配置加载成功');
            }
        } catch (error) {
            console.error('配置加载失败:', error);
            this.showMessage('configResult', 'error', '配置加载失败: ' + error.message);
        }
    }

    refresh() {
        this.updateStats();
        this.renderLicenses();
    }

    updateStats() {
        const stats = { total: 0, dragon: 0, annual: 0, trial: 0 };
        this.licenses.forEach(license => {
            stats.total++;
            stats[license.licenseType]++;
        });
        Object.keys(stats).forEach(key => this.setElementText(key + 'Count', stats[key]));
    }

    renderLicenses() {
        const container = document.getElementById('licensesList');
        if (!this.licenses.length) {
            container.innerHTML = '<div class="empty-state">暂无数据</div>';
            return;
        }
        const groupByType = document.getElementById('groupByType').checked;
        container.innerHTML = groupByType ? this.renderGrouped() : this.renderFlat();
    }

    renderFlat() {
        return this.licenses.map(license => this.renderLicenseItem(license)).join('');
    }

    renderGrouped() {
        const groups = this.groupLicensesByType();
        return Object.keys(groups).map(type => {
            const licenses = groups[type];
            if (!licenses.length) return '';
            return `
                <div class="group-header">
                    <span>${this.getTypeName(type)} (${licenses.length}个)</span>
                    <label><input type="checkbox" onchange="App.toggleGroupSelection('${type}', this.checked)"> 全选</label>
                </div>
                ${licenses.map(license => this.renderLicenseItem(license)).join('')}
            `;
        }).join('');
    }

    renderLicenseItem(license) {
        // 使用userId作为唯一标识符
        const licenseId = license.userId;
        const statusBtn = license.status === 'active'
            ? `<button class="btn btn-used btn-sm" onclick="App.markAsUsed('${licenseId}')">已用</button>`
            : `<button class="btn btn-activate btn-sm" onclick="App.markAsActive('${licenseId}')">未用</button>`;

        // 显示密钥前8位
        const keyDisplay = license.key ? license.key.substring(0, 8) + '...' : '无密钥';

        return `
            <div class="list-item" data-license-id="${licenseId}" style="grid-template-columns: 50px 2.5fr 1fr 1fr 1.5fr 1.5fr 1fr 2fr;">
                <div><input type="checkbox" name="selectLicense" value="${licenseId}" onchange="App.updateSelection()"></div>
                <div>
                    <div class="item-code">${license.code}</div>
                    <div class="item-meta">${license.userName} (${license.userId})</div>
                </div>
                <div><span class="badge badge-${license.licenseType}">${this.getTypeName(license.licenseType)}</span></div>
                <div>设备: ${license.maxDevices}</div>
                <div>密钥: ${keyDisplay}</div>
                <div>${license.notes || '无备注'}</div>
                <div><span class="badge badge-${license.status}">${license.status === 'active' ? '有效' : '已用'}</span></div>
                <div class="actions">
                    ${statusBtn}
                    <button class="btn btn-delete btn-sm" onclick="App.deleteLicense('${licenseId}')">删除</button>
                    <button class="btn btn-copy btn-sm" onclick="App.copyToClipboard('${license.code}')" title="复制激活码">复制</button>
                </div>
            </div>
        `;
    }

    groupLicensesByType() {
        return this.licenses.reduce((groups, license) => {
            const type = license.licenseType;
            if (!groups[type]) groups[type] = [];
            groups[type].push(license);
            return groups;
        }, {});
    }

    filterLicenses() {
        const search = document.getElementById('searchInput').value.toLowerCase();
        const type = document.getElementById('typeFilter').value;
        const timeFilter = document.getElementById('timeFilter').value;

        const filtered = this.licenses.filter(license => {
            const matchSearch = !search || license.code.toLowerCase().includes(search) || license.userName.toLowerCase().includes(search);
            const matchType = !type || license.licenseType === type;
            const matchTime = this.matchTimeFilter(license.createdAt, timeFilter);
            return matchSearch && matchType && matchTime;
        });

        const original = this.licenses;
        this.licenses = filtered;
        this.renderLicenses();
        this.licenses = original;
    }

    matchTimeFilter(timestamp, filter) {
        if (!filter) return true;
        const now = new Date();
        const date = new Date(timestamp);
        const filters = {
            today: () => date.toDateString() === now.toDateString(),
            week: () => date >= new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
            month: () => date >= new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        };
        return filters[filter] ? filters[filter]() : true;
    }

    // 激活码操作
    async markAsUsed(licenseId) {
        if (confirm('确定标记为已使用？')) {
            await this.updateLicenseStatus(licenseId, 'used', '已标记为已使用');
        }
    }

    async markAsActive(licenseId) {
        if (confirm('确定重新激活？')) {
            await this.updateLicenseStatus(licenseId, 'active', '已重新激活');
        }
    }

    async updateLicenseStatus(licenseId, status, message) {
        try {
            const url = status === 'used' ? `/api/mark-used/${licenseId}` : `/api/mark-active/${licenseId}`;
            const response = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' } });
            const result = await response.json();

            if (result.success) {
                const license = this.licenses.find(l => l.id === licenseId);
                if (license) {
                    license.status = status;
                    if (status === 'used') license.usedAt = Date.now();
                    else delete license.usedAt;
                }
                this.refresh();
                this.showMessage('', 'success', message);
            } else {
                this.showMessage('', 'error', `操作失败: ${result.error}`);
            }
        } catch (error) {
            this.showMessage('', 'error', `请求失败: ${error.message}`);
        }
    }

    async deleteLicense(licenseId) {
        const license = this.licenses.find(l => l.userId === licenseId);
        if (!license || !confirm(`确定删除 "${license.code}"？\n此操作不可撤销！`)) return;

        try {
            const response = await fetch(`/api/license/${licenseId}`, { method: 'DELETE' });
            const result = await response.json();

            if (result.success) {
                this.licenses = this.licenses.filter(l => l.userId !== licenseId);
                this.refresh();
                this.showMessage('', 'success', '激活码已删除');
            } else {
                this.showMessage('', 'error', `删除失败: ${result.error}`);
            }
        } catch (error) {
            this.showMessage('', 'error', `请求失败: ${error.message}`);
        }
    }

    refreshLicenses() { this.loadLicenses(); }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showMessage('', 'success', '激活码已复制到剪贴板');
        } catch (error) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                this.showMessage('', 'success', '激活码已复制到剪贴板');
            } catch (err) {
                this.showMessage('', 'error', '复制失败，请手动复制');
            }
            document.body.removeChild(textArea);
        }
    }

    updateSelection() { this.updateBatchControls(); }

    updateBatchControls() {
        const checkboxes = document.querySelectorAll('input[name="selectLicense"]:checked');
        const count = checkboxes.length;
        const batchControls = document.getElementById('batchControls');
        const selectedCount = document.getElementById('selectedCount');

        this.toggleElement(batchControls, count > 0);
        if (selectedCount) selectedCount.textContent = `已选择 ${count} 项`;
    }

    toggleGroupSelection(type, checked) {
        document.querySelectorAll('input[name="selectLicense"]').forEach(checkbox => {
            const license = this.licenses.find(l => l.userId === checkbox.value);
            if (license && license.licenseType === type) checkbox.checked = checked;
        });
        this.updateBatchControls();
    }

    clearSelection() {
        document.querySelectorAll('input[name="selectLicense"]').forEach(cb => cb.checked = false);
        this.updateBatchControls();
    }

    getSelectedIds() {
        return Array.from(document.querySelectorAll('input[name="selectLicense"]:checked')).map(cb => cb.value);
    }

    // 批量操作
    async batchMarkUsed() {
        const ids = this.getSelectedIds();
        if (!ids.length || !confirm(`确定将 ${ids.length} 个激活码标记为已使用？`)) return;
        await this.batchOperation(ids, 'used', '批量标记已使用成功');
    }

    async batchMarkActive() {
        const ids = this.getSelectedIds();
        if (!ids.length || !confirm(`确定将 ${ids.length} 个激活码重新激活？`)) return;
        await this.batchOperation(ids, 'active', '批量激活成功');
    }

    async batchDelete() {
        const ids = this.getSelectedIds();
        if (!ids.length || !confirm(`确定删除 ${ids.length} 个激活码？\n此操作不可撤销！`)) return;

        try {
            const response = await fetch('/api/batch-delete', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ids })
            });

            const result = await response.json();
            if (result.success) {
                this.licenses = this.licenses.filter(l => !ids.includes(l.userId));
                this.refresh();
                this.clearSelection();
                this.showMessage('', 'success', `批量删除成功: ${ids.length}个`);
            } else {
                this.showMessage('', 'error', `批量删除失败: ${result.error}`);
            }
        } catch (error) {
            this.showMessage('', 'error', `请求失败: ${error.message}`);
        }
    }

    async batchOperation(ids, status, successMessage) {
        try {
            const response = await fetch('/api/batch-update-status', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ids, status })
            });

            const result = await response.json();
            if (result.success) {
                ids.forEach(id => {
                    const license = this.licenses.find(l => l.userId === id);
                    if (license) {
                        license.status = status;
                    }
                });
                this.refresh();
                this.clearSelection();
                this.showMessage('', 'success', `${successMessage}: ${ids.length}个`);
            } else {
                this.showMessage('', 'error', `操作失败: ${result.error}`);
            }
        } catch (error) {
            this.showMessage('', 'error', `请求失败: ${error.message}`);
        }
    }

    showExportDialog() {
        const type = document.getElementById('typeFilter').value;
        const count = type ? this.licenses.filter(l => l.licenseType === type).length : this.licenses.length;
        const typeName = type ? this.getTypeName(type) : '全部类型';

        this.setElementText('exportRange', typeName);
        this.setElementText('exportCount', count);
        this.showElement('exportDialog');
    }

    hideExportDialog() {
        this.hideElement('exportDialog');
    }

    async exportLicenses() {
        const format = document.getElementById('exportFormat').value;
        const type = document.getElementById('typeFilter').value;

        this.downloadFile(`/api/export/${format}${type ? `?type=${type}` : ''}`);
        this.hideExportDialog();
        this.showMessage('', 'success', '导出成功，文件已开始下载');
    }

    // 工具方法
    getTypeName(type) {
        const names = { dragon: '恶龙会员', annual: '年付会员', trial: '体验会员' };
        return names[type] || type;
    }

    formatDate(timestamp) { return new Date(timestamp).toLocaleDateString(); }
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    setStatus(statusId, text, type = '') {
        const el = document.getElementById(statusId);
        if (el) {
            el.textContent = `状态: ${text}`;
            el.className = `cloud-status ${type}`;
        }
    }

    showMessage(elementId, type, message) {
        if (elementId) {
            const el = document.getElementById(elementId);
            if (el) {
                el.className = `result-panel result-${type}`;
                el.textContent = message;
                this.showElement(el);
                setTimeout(() => this.hideElement(el), 5000);
            }
        } else {
            this.showToast(message, type);
        }
    }

    // DOM操作工具
    showElement(element) {
        const el = typeof element === 'string' ? document.getElementById(element) : element;
        if (el) el.style.display = el.tagName === 'DIV' ? 'block' : 'flex';
    }

    hideElement(element) {
        const el = typeof element === 'string' ? document.getElementById(element) : element;
        if (el) el.style.display = 'none';
    }

    toggleElement(element, show) {
        if (show) this.showElement(element);
        else this.hideElement(element);
    }

    setElementText(elementId, text) {
        const el = document.getElementById(elementId);
        if (el) el.textContent = text;
    }

    downloadFile(url) {
        const link = document.createElement('a');
        link.href = url;
        link.download = true;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(() => document.body.removeChild(toast), 3000);
    }

    // ==================== S3管理功能 ====================

    // S3文件列表
    s3Files = [];

    // 刷新S3文件列表
    async refreshS3List() {
        try {
            const response = await fetch('/api/s3/list');
            const result = await response.json();

            if (result.success) {
                this.s3Files = result.files || [];
                this.renderCompareView();
                this.updateS3Stats();
                const s3Count = this.s3Files.filter(f => f.Key.endsWith('.dat')).length;
                this.showToast(`获取到 ${s3Count} 个云端激活码`, 'success');
            } else {
                this.showToast(result.error || 'S3列表获取失败', 'error');
            }
        } catch (error) {
            this.showToast(`S3连接失败: ${error.message}`, 'error');
        }
    }

    // 渲染比较列表
    renderS3Files() {
        this.renderCompareView();
    }

    renderCompareView() {
        const localContainer = document.getElementById('localFilesList');
        const s3Container = document.getElementById('s3FilesList');

        // 获取激活码列表
        const localCodes = this.licenses.map(l => l.code);
        const s3Codes = this.s3Files.filter(f => f.Key.endsWith('.dat')).map(f => f.Key.replace('.dat', ''));

        // 创建一一对应的比较列表
        let allCodes = [...new Set([...localCodes, ...s3Codes])].sort();

        // 应用筛选
        allCodes = this.applyCompareFilters(allCodes, localCodes, s3Codes);

        // 渲染对应列表
        localContainer.innerHTML = this.renderAlignedList(allCodes, localCodes, 'local');
        s3Container.innerHTML = this.renderAlignedList(allCodes, s3Codes, 's3');

        // 更新统计信息
        this.updateCompareStats(localCodes, s3Codes, allCodes);
    }

    renderAlignedList(allCodes, currentCodes, type) {
        if (!allCodes.length) {
            return '<div class="loading">暂无数据</div>';
        }

        const localCodes = this.licenses.map(l => l.code);
        const s3Codes = this.s3Files.filter(f => f.Key.endsWith('.dat')).map(f => f.Key.replace('.dat', ''));

        return allCodes.map(code => {
            const exists = currentCodes.includes(code);
            const license = this.licenses.find(l => l.code === code);
            const meta = license ? `${license.licenseType} | ${license.userName}` : '';

            if (!exists) {
                // 不存在的项目显示为空占位
                return `
                    <div class="compare-item empty">
                        <div>
                            <div class="compare-code">--- 空位 ---</div>
                            <div class="compare-meta">此处无激活码</div>
                        </div>
                        <div class="compare-actions">
                            ${type === 'local' && s3Codes.includes(code) ? `<button class="btn btn-sm" onclick="App.downloadS3File('${code}.dat')">下载</button>` : ''}
                            ${type === 's3' && localCodes.includes(code) ? `<button class="btn btn-sm" onclick="App.uploadSingleCode('${code}')">上传</button>` : ''}
                        </div>
                    </div>
                `;
            }

            // 存在的项目显示完整信息和操作按钮
            const otherSideExists = type === 'local' ? s3Codes.includes(code) : localCodes.includes(code);
            const cssClass = otherSideExists ? 'same' : 'missing';

            return `
                <div class="compare-item ${cssClass}">
                    <div>
                        <div class="compare-code">${code}</div>
                        ${meta ? `<div class="compare-meta">${meta}</div>` : ''}
                    </div>
                    <div class="compare-actions">
                        <button class="btn btn-copy btn-sm" onclick="App.copyCode('${code}')">复制</button>
                        ${type === 'local' ? `<button class="btn btn-secondary btn-sm" onclick="App.deleteLicense('${code}')">删除</button>` : ''}
                        ${type === 'local' && !s3Codes.includes(code) ? `<button class="btn btn-sm" onclick="App.uploadSingleCode('${code}')">上传</button>` : ''}
                        ${type === 's3' ? `<button class="btn btn-sm" onclick="App.downloadS3File('${code}.dat')">下载</button>` : ''}
                        ${type === 's3' ? `<button class="btn btn-secondary btn-sm" onclick="App.deleteS3File('${code}.dat')">删除</button>` : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    // 同步滚动
    syncScroll(source, targetId) {
        if (this.isScrolling) return; // 防止循环滚动
        this.isScrolling = true;

        const target = document.getElementById(targetId);
        if (target) {
            target.scrollTop = source.scrollTop;
        }

        setTimeout(() => {
            this.isScrolling = false;
        }, 10);
    }

    // 上传单个激活码
    async uploadSingleCode(code) {
        if (!confirm(`上传激活码 "${code}" 到云端？`)) return;

        try {
            // 找到对应的激活码
            const license = this.licenses.find(l => l.code === code);
            if (!license) {
                this.showToast('激活码不存在', 'error');
                return;
            }

            // 调用上传API（这里复用现有的上传逻辑）
            const response = await fetch('/api/upload-qiniu', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ singleCode: code })
            });

            const result = await response.json();
            if (result.success) {
                this.showToast(`${code} 上传成功`, 'success');
                this.refreshS3List(); // 刷新比较视图
            } else {
                this.showToast(result.error || '上传失败', 'error');
            }
        } catch (error) {
            this.showToast(`上传失败: ${error.message}`, 'error');
        }
    }

    // 应用比较筛选
    applyCompareFilters(allCodes, localCodes, s3Codes) {
        const searchTerm = document.getElementById('compareSearch')?.value.toLowerCase() || '';
        const typeFilter = document.getElementById('compareTypeFilter')?.value || 'all';
        const statusFilter = document.getElementById('compareStatusFilter')?.value || 'all';

        return allCodes.filter(code => {
            // 搜索筛选
            if (searchTerm && !code.toLowerCase().includes(searchTerm)) {
                return false;
            }

            // 类型筛选
            if (typeFilter !== 'all') {
                const license = this.licenses.find(l => l.code === code);
                if (!license || license.licenseType !== typeFilter) {
                    return false;
                }
            }

            // 状态筛选
            if (statusFilter !== 'all') {
                const inLocal = localCodes.includes(code);
                const inS3 = s3Codes.includes(code);

                switch (statusFilter) {
                    case 'both':
                        if (!inLocal || !inS3) return false;
                        break;
                    case 'local-only':
                        if (!inLocal || inS3) return false;
                        break;
                    case 's3-only':
                        if (inLocal || !inS3) return false;
                        break;
                    case 'missing':
                        if (inLocal && inS3) return false;
                        break;
                }
            }

            return true;
        });
    }

    // 筛选比较列表
    filterCompareList() {
        this.renderCompareView();
    }

    // 清除筛选
    clearCompareFilters() {
        document.getElementById('compareSearch').value = '';
        document.getElementById('compareTypeFilter').value = 'all';
        document.getElementById('compareStatusFilter').value = 'all';
        this.renderCompareView();
    }

    // 刷新比较视图
    refreshCompareView() {
        this.renderCompareView();
    }

    // 更新比较统计
    updateCompareStats(localCodes, s3Codes, filteredCodes) {
        // 基于筛选后的结果计算统计
        const filteredLocalCodes = filteredCodes.filter(code => localCodes.includes(code));
        const filteredS3Codes = filteredCodes.filter(code => s3Codes.includes(code));

        // 计算缺失数量
        const localMissing = filteredCodes.filter(code => !localCodes.includes(code)).length;
        const s3Missing = filteredCodes.filter(code => !s3Codes.includes(code)).length;

        // 更新总计和缺失统计
        document.getElementById('localTotalCount').textContent = filteredLocalCodes.length;
        document.getElementById('localMissingCount').textContent = localMissing;
        document.getElementById('s3TotalCount').textContent = filteredS3Codes.length;
        document.getElementById('s3MissingCount').textContent = s3Missing;

        // 更新类型统计
        this.updateCompareTypeStats(filteredLocalCodes, filteredS3Codes);
    }

    // 更新比较类型统计
    updateCompareTypeStats(localCodes, s3Codes) {
        // 本地类型统计
        const localTypeStats = this.calculateTypeStats(localCodes);
        document.getElementById('localStatsTypes').innerHTML = this.renderTypeStats(localTypeStats);

        // 云端类型统计（基于本地数据推断）
        const s3TypeStats = this.calculateTypeStats(s3Codes);
        document.getElementById('s3StatsTypes').innerHTML = this.renderTypeStats(s3TypeStats);
    }

    // 计算类型统计
    calculateTypeStats(codes) {
        const stats = { dragon: 0, annual: 0, trial: 0, unknown: 0 };

        codes.forEach(code => {
            const license = this.licenses.find(l => l.code === code);
            if (license) {
                stats[license.licenseType] = (stats[license.licenseType] || 0) + 1;
            } else {
                stats.unknown++;
            }
        });

        return stats;
    }

    // 渲染类型统计
    renderTypeStats(stats) {
        const typeNames = {
            dragon: '龙年版',
            annual: '年度版',
            trial: '试用版',
            unknown: '未知'
        };

        return Object.entries(stats)
            .filter(([type, count]) => count > 0)
            .map(([type, count]) => `<span>${typeNames[type]}: <strong>${count}</strong></span>`)
            .join('');
    }

    // 更新统计
    updateS3Stats() {
        const s3Count = this.s3Files.filter(f => f.Key.endsWith('.dat')).length;
        document.getElementById('s3FileCount').textContent = s3Count;
        document.getElementById('localFileCount').textContent = this.licenses.length;
    }

    // 云端同步
    uploadToS3() { this.apiCall('/api/upload-qiniu', {}, 's3Result', '上传'); }
    downloadFromS3() { this.apiCall('/api/download-qiniu', {}, 's3Result', '下载'); }

    // 复制激活码
    copyCode(code) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(code).then(() => {
                this.showToast(`激活码 ${code} 已复制`, 'success');
            }).catch(() => {
                this.fallbackCopy(code);
            });
        } else {
            this.fallbackCopy(code);
        }
    }

    fallbackCopy(code) {
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            this.showToast(`激活码 ${code} 已复制`, 'success');
        } catch (err) {
            this.showToast('复制失败，请手动复制', 'error');
        }
        document.body.removeChild(textArea);
    }

    // 文件操作
    async downloadS3File(key) {
        if (!confirm(`下载 "${key}"？`)) return;
        const response = await fetch(`/api/s3/download/${encodeURIComponent(key)}`);
        const result = await response.json();
        if (result.success) {
            this.showToast(`${key} 下载成功`, 'success');
            this.loadData();
            this.refreshS3List(); // 刷新比较视图
        } else {
            this.showToast(result.error || '下载失败', 'error');
        }
    }

    async deleteS3File(key) {
        if (!confirm(`删除云端文件 "${key}"？`)) return;
        const response = await fetch(`/api/s3/delete/${encodeURIComponent(key)}`, { method: 'DELETE' });
        const result = await response.json();
        if (result.success) {
            this.showToast(`${key} 删除成功`, 'success');
            this.refreshS3List(); // 刷新比较视图
        } else {
            this.showToast(result.error || '删除失败', 'error');
        }
    }

    // 配置预设
    updateS3Config() {
        const provider = document.getElementById('s3Provider').value;
        const configs = {
            qiniu: { endpoint: 'https://s3.cn-south-1.qiniucs.com', region: 'cn-south-1' },
            aliyun: { endpoint: 'https://oss-cn-hangzhou.aliyuncs.com', region: 'cn-hangzhou' },
            tencent: { endpoint: 'https://cos.ap-beijing.myqcloud.com', region: 'ap-beijing' },
            aws: { endpoint: 'https://s3.amazonaws.com', region: 'us-east-1' },
            minio: { endpoint: 'http://localhost:9000', region: 'us-east-1' }
        };
        if (configs[provider]) {
            document.getElementById('configEndpoint').value = configs[provider].endpoint;
            document.getElementById('configRegion').value = configs[provider].region;
        }
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

window.App = new LicenseApp();
