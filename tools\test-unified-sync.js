const http = require('http');

// 测试统一同步接口
async function testSync(action = 'sync') {
    console.log(`🔄 测试${action === 'sync' ? '双向同步' : action === 'upload' ? '上传' : '下载'}...`);
    
    const req = http.request({
        hostname: 'localhost', port: 3002, path: '/api/sync/performSync', method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    }, res => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            const result = JSON.parse(data);
            console.log('同步结果:', result.success ? '✅ 成功' : '❌ 失败');
            console.log('操作类型:', result.action);
            console.log('详细信息:', result.message);
            console.log('总激活码数:', result.total);
            if (result.error) console.log('错误:', result.error);
        });
    });
    
    req.write(JSON.stringify({ action }));
    req.end();
}

// 测试不同的同步操作
async function runTests() {
    console.log('🎯 测试思源笔记风格的统一同步接口\n');
    
    // 测试双向同步
    await new Promise(resolve => {
        testSync('sync');
        setTimeout(resolve, 2000);
    });
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 测试单独上传
    await new Promise(resolve => {
        testSync('upload');
        setTimeout(resolve, 2000);
    });
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 测试单独下载
    await new Promise(resolve => {
        testSync('download');
        setTimeout(resolve, 2000);
    });
}

runTests();
