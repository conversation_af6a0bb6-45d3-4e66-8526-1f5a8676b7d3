/**
 * 🔧 七牛云配置管理器 - 极限精简版
 *
 * 功能特性:
 * - 内置默认配置，无需外部文件
 * - 支持动态修改和环境变量覆盖
 * - 明文显示所有配置，统一管理
 * - 配置验证和错误处理
 *
 * 使用方法:
 * const config = require('./qiniu-config');
 * console.log(config.get());           // 获取完整配置
 * config.update({bucket: 'new'});      // 更新配置
 * console.log(config.validate());      // 验证配置
 */

class QiniuConfigManager {
    constructor() {
        // 内置默认配置 - 无需外部文件
        this.config = {
            accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
            secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
            bucket: 'siyuan-mediaplayer',
            region: 'Zone_z2',
            domain: 'http://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer',
            fileName: 'licenses.json',
            encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This' // 固定密钥
        };

        // 区域映射
        this.regions = {
            'Zone_z0': '华东-浙江',
            'Zone_z1': '华北-河北',
            'Zone_z2': '华南-广东',
            'Zone_na0': '北美-洛杉矶',
            'Zone_as0': '亚太-新加坡'
        };

        // 配置说明
        this.description = {
            accessKey: '七牛云控制台 > 密钥管理 > AccessKey',
            secretKey: '七牛云控制台 > 密钥管理 > SecretKey',
            bucket: '七牛云控制台 > 对象存储 > 空间名称',
            region: '存储空间所在区域，影响访问速度',
            domain: '用于访问文件的完整域名地址',
            fileName: '上传到七牛云的激活码文件名',
            encryptionKey: '激活码数据加密密钥，请妥善保管'
        };

        this.updateFromEnv();
    }

    // 获取完整配置
    get() {
        return {
            ...this.config,
            regions: this.regions,
            description: this.description
        };
    }

    // 获取原始配置(向后兼容)
    getRaw() { return { ...this.config }; }

    // 更新配置
    update(newConfig) {
        this.config = { ...this.config, ...newConfig };
        return { success: true, message: '配置更新成功' };
    }

    // 验证配置完整性
    validate() {
        const required = ['accessKey', 'secretKey', 'bucket', 'region'];
        const missing = required.filter(key => !this.config[key]);
        return missing.length === 0 ? { valid: true } : { valid: false, missing };
    }

    // 从环境变量更新
    updateFromEnv() {
        const envMapping = {
            QINIU_ACCESS_KEY: 'accessKey',
            QINIU_SECRET_KEY: 'secretKey',
            QINIU_BUCKET: 'bucket',
            QINIU_REGION: 'region',
            QINIU_DOMAIN: 'domain',
            ENCRYPTION_KEY: 'encryptionKey'
        };

        let updated = false;
        Object.entries(envMapping).forEach(([envKey, configKey]) => {
            if (process.env[envKey]) {
                this.config[configKey] = process.env[envKey];
                updated = true;
            }
        });

        if (updated) {
            console.log('✅ 已从环境变量更新七牛云配置');
        }
    }

    // 获取七牛SDK配置
    getSDKConfig() {
        return {
            accessKey: this.config.accessKey,
            secretKey: this.config.secretKey,
            bucket: this.config.bucket,
            region: this.config.region,
            domain: this.config.domain,
            fileName: this.config.fileName
        };
    }

    // 获取加密配置
    getEncryptionConfig() {
        return { key: this.config.encryptionKey, algorithm: 'aes-256-cbc' };
    }


}

// 导出单例实例
module.exports = new QiniuConfigManager();
