/**
 * 许可证管理器 - 极简版
 */

import { QiniuLicenseValidator } from "./qiniu-validator";

export interface LicenseInfo {
    code: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    maxDevices: number;
    isValid: boolean;
    features: string[];
    restrictions?: { maxCloudFiles: number; maxBatchImport: number };
}

export class LicenseManager {
    // 字符集定义
    private static readonly CHARS = "ABCDEFGHJKMNPQRSTUVWXYZ23456789";

    // 本地体验会员配置
    private static readonly TRIAL_DURATION = 7 * 24 * 60 * 60 * 1000; // 7天体验期

    // 简化的激活码验证 - 只进行基本格式检查
    private static validateCodeFormat(code: string): boolean {
        return /^[A-Z0-9]{24}$/.test(code) && code.split('').every(char => this.CHARS.includes(char));
    }

    private static getTypeInfo(type: string): { devices: number; days: number } {
        const info = { dragon: { devices: 5, days: 0 }, annual: { devices: 5, days: 365 }, trial: { devices: 1, days: 7 } };
        return info[type] || { devices: 1, days: 7 };
    }

    private static getFeaturesForType(type: string): string[] {
        const features = {
            dragon: ['unlimited_cloud', 'batch_import', 'priority_support', 'advanced_features'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            trial: ['basic_features']
        };
        return features[type] || features.trial;
    }

    // 验证激活码 - 优先使用七牛云验证
    static async validateLicense(code: string, actualUserId?: string): Promise<{ success: boolean; data?: LicenseInfo; error?: string }> {
        try {
            if (!code || code.length < 10) return { success: false, error: '激活码格式错误' };

            const cleanCode = code.replace(/[-\s]/g, '').toUpperCase();

            // 基本格式验证
            if (!this.validateCodeFormat(cleanCode)) {
                return { success: false, error: '激活码格式不正确' };
            }

            // 1. 优先使用七牛云在线验证
            try {
                const qiniuResult = await QiniuLicenseValidator.validateLicense(cleanCode);
                if (qiniuResult.success && qiniuResult.data) {
                    const qiniuLicense = qiniuResult.data;

                    // 转换为LicenseInfo格式
                    const licenseInfo: LicenseInfo = {
                        code: cleanCode,
                        userId: actualUserId || 'online_user',
                        userName: actualUserId ? '当前用户' : '在线用户',
                        activatedAt: Date.now(),
                        expiresAt: qiniuLicense.expiryTimestamp,
                        type: qiniuLicense.type,
                        maxDevices: qiniuLicense.maxDevices,
                        isValid: true,
                        features: this.getFeaturesForType(qiniuLicense.type),
                        ...(qiniuLicense.type === 'trial' && { restrictions: { maxCloudFiles: 100, maxBatchImport: 20 } })
                    };

                    console.log('✅ 七牛云验证成功:', qiniuLicense.type);
                    return { success: true, data: licenseInfo };
                }
            } catch (qiniuError) {
                console.warn('⚠️ 七牛云验证失败，使用基本验证:', qiniuError);
            }

            // 2. 回退到基本验证（假设为trial类型）
            const licenseInfo: LicenseInfo = {
                code: cleanCode,
                userId: actualUserId || 'local_user',
                userName: actualUserId ? '当前用户' : '本地用户',
                activatedAt: Date.now(),
                expiresAt: Date.now() + this.TRIAL_DURATION,
                type: 'trial',
                maxDevices: 1,
                isValid: true,
                features: this.getFeaturesForType('trial'),
                restrictions: { maxCloudFiles: 100, maxBatchImport: 20 }
            };

            console.log('✅ 基本验证通过，默认为体验版');
            return { success: true, data: licenseInfo };

        } catch (error) {
            return { success: false, error: error.message || '激活码验证失败' };
        }
    }

    // 生成体验激活码（简化版）
    private static generateTrialCode(userId: string): string {
        const now = Date.now();
        const randomPart = Math.random().toString(36).substring(2, 15);
        const userPart = userId.substring(0, 4).padEnd(4, '0');
        const timePart = (now % 1000000).toString().padStart(6, '0');
        
        // 组合并填充到24位
        let code = (randomPart + userPart + timePart).toUpperCase().substring(0, 24);
        
        // 确保只包含有效字符
        code = code.split('').map(char => 
            this.CHARS.includes(char) ? char : this.CHARS[Math.floor(Math.random() * this.CHARS.length)]
        ).join('');
        
        return code.padEnd(24, this.CHARS[0]);
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: this.generateTrialCode(user.userId),
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + this.TRIAL_DURATION,
            type: 'trial',
            maxDevices: 1,
            isValid: true,
            features: this.getFeaturesForType('trial'),
            restrictions: { maxCloudFiles: 100, maxBatchImport: 20 }
        };
    }

    // 检查许可证是否过期
    static isLicenseExpired(license: LicenseInfo): boolean {
        if (license.type === 'dragon') return false; // 永久许可证
        return license.expiresAt > 0 && license.expiresAt < Date.now();
    }

    // 获取许可证剩余天数
    static getRemainingDays(license: LicenseInfo): number {
        if (license.type === 'dragon') return -1; // 永久
        if (license.expiresAt <= 0) return -1;
        
        const remaining = license.expiresAt - Date.now();
        return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
    }

    // 检查功能是否可用
    static hasFeature(license: LicenseInfo, feature: string): boolean {
        return license.isValid && !this.isLicenseExpired(license) && license.features.includes(feature);
    }

    // 获取许可证状态描述
    static getLicenseStatus(license: LicenseInfo): string {
        if (!license.isValid) return '无效';
        if (this.isLicenseExpired(license)) return '已过期';
        
        const remaining = this.getRemainingDays(license);
        if (remaining === -1) return '永久有效';
        if (remaining === 0) return '今天到期';
        if (remaining <= 7) return `${remaining}天后到期`;
        
        return '有效';
    }
}
