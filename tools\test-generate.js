/**
 * 测试激活码生成脚本
 */

const encryption = require('./encryption');
const fs = require('fs');
const path = require('path');

console.log('🎲 生成测试激活码...\n');

// 生成不同类型的激活码
const licenses = [];

// 生成恶龙会员激活码
for (let i = 1; i <= 3; i++) {
    const license = encryption.generateLicense('dragon', `dragon_user_${i}`, `恶龙会员_${i}`, '测试生成');
    licenses.push(license);
    console.log(`恶龙会员 ${i}: ${license.code}`);
}

console.log('');

// 生成年付会员激活码
for (let i = 1; i <= 3; i++) {
    const license = encryption.generateLicense('annual', `annual_user_${i}`, `年付会员_${i}`, '测试生成');
    licenses.push(license);
    console.log(`年付会员 ${i}: ${license.code}`);
}

console.log('');

// 生成体验会员激活码
for (let i = 1; i <= 3; i++) {
    const license = encryption.generateLicense('trial', `trial_user_${i}`, `体验会员_${i}`, '测试生成');
    licenses.push(license);
    console.log(`体验会员 ${i}: ${license.code}`);
}

// 保存到文件
const licensesFile = path.join(__dirname, 'license-data', 'licenses.json');
fs.writeFileSync(licensesFile, JSON.stringify(licenses, null, 2));

console.log(`\n✅ 已生成 ${licenses.length} 个激活码并保存到 licenses.json`);

// 测试解析
console.log('\n🔍 测试激活码解析:');
licenses.slice(0, 3).forEach((license, index) => {
    try {
        const parsed = encryption.parseActivationCode(license.code);
        console.log(`${index + 1}. ${license.code} -> 类型: ${parsed.licenseType}, 时间: ${parsed.timeInfo ? `${parsed.timeInfo.year}-${parsed.timeInfo.month}-${parsed.timeInfo.day}` : '解析失败'}`);
    } catch (error) {
        console.log(`${index + 1}. ${license.code} -> 解析失败: ${error.message}`);
    }
});

console.log('\n🎯 测试完成！');
