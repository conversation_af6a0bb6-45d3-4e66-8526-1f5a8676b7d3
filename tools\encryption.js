/**
 * 🔐 思源笔记激活码系统 - 极简版
 *
 * === 核心特性 ===
 * - 24位激活码，时间混淆到1,3,5,7,9位
 * - 随机密钥生成，每个激活码独立加密
 * - 极简数据结构：key, code, userId, userName, maxDevices, notes, status
 * - 高效加密解密，优雅完美设计
 *
 * === 激活码结构 ===
 * 位置1,3,5,7,9: 时间信息(5位)
 * 位置2,4,6,8,10: 类型+随机(5位) 
 * 位置11-20: 用户信息(10位)
 * 位置21-24: 校验码(4位)
 *
 * === 使用方法 ===
 * const crypto = require('./encryption');
 * const license = crypto.generateLicense('dragon', 'user123', '用户名');
 * const decrypted = crypto.decryptLicenseList(encryptedData);
 */

const crypto = require('crypto');
const zlib = require('zlib');

class EncryptionManager {
    constructor() {
        this.algorithm = 'aes-256-cbc';
        this.charset = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789'; // 29个字符，排除易混淆字符
    }

    // 生成随机密钥
    generateRandomKey() {
        return crypto.randomBytes(32).toString('hex'); // 64位十六进制字符串
    }

    // 生成完整的激活码许可证
    generateLicense(licenseType, userId, userName, notes = '') {
        const key = this.generateRandomKey();
        const code = this.generateActivationCode(licenseType, userId);
        const maxDevices = this.getMaxDevices(licenseType);
        
        return {
            key,
            code,
            userId,
            userName,
            maxDevices,
            notes,
            status: 'active'
        };
    }

    // 生成激活码 - 时间混淆到1,3,5,7,9位
    generateActivationCode(licenseType, userId) {
        const timestamp = Date.now();
        const timeDigits = (timestamp % 100000).toString().padStart(5, '0'); // 5位时间
        
        // 类型编码
        const typeMap = { dragon: 1, annual: 2, trial: 3 };
        const typeCode = typeMap[licenseType] || 3;
        
        // 构建24位激活码
        let code = new Array(24);
        
        // 时间混淆到1,3,5,7,9位 (索引0,2,4,6,8)
        for (let i = 0; i < 5; i++) {
            code[i * 2] = this.charset[parseInt(timeDigits[i])];
        }
        
        // 类型和随机填充到2,4,6,8,10位 (索引1,3,5,7,9)
        code[1] = this.charset[typeCode];
        for (let i = 3; i < 10; i += 2) {
            code[i] = this.charset[crypto.randomInt(0, this.charset.length)];
        }
        
        // 用户信息编码到11-20位 (索引10-19)
        const userHash = this.hashUserId(userId);
        for (let i = 0; i < 10; i++) {
            code[10 + i] = this.charset[userHash[i] % this.charset.length];
        }
        
        // 校验码到21-24位 (索引20-23)
        const checksum = this.calculateChecksum(code.slice(0, 20).join('') + licenseType);
        for (let i = 0; i < 4; i++) {
            code[20 + i] = this.charset[(checksum + i) % this.charset.length];
        }
        
        return code.join('');
    }

    // 解析激活码，提取时间和类型信息
    parseActivationCode(code) {
        if (!code || code.length !== 24) {
            throw new Error('激活码格式错误');
        }

        // 提取时间信息 (位置1,3,5,7,9)
        let timeStr = '';
        for (let i = 0; i < 5; i++) {
            const char = code[i * 2];
            const index = this.charset.indexOf(char);
            if (index === -1) throw new Error('激活码包含无效字符');
            timeStr += index.toString().padStart(1, '0');
        }
        
        // 提取类型信息 (位置2)
        const typeIndex = this.charset.indexOf(code[1]);
        const typeMap = { 1: 'dragon', 2: 'annual', 3: 'trial' };
        const licenseType = typeMap[typeIndex] || 'trial';
        
        // 计算时间戳 (近似值)
        const timeDigits = parseInt(timeStr);
        const currentTime = Date.now();
        const baseTime = Math.floor(currentTime / 100000) * 100000;
        const timestamp = baseTime + timeDigits;
        
        return {
            licenseType,
            timestamp,
            timeDigits
        };
    }

    // 哈希用户ID
    hashUserId(userId) {
        const hash = crypto.createHash('sha256').update(userId).digest();
        return Array.from(hash.slice(0, 10));
    }

    // 获取最大设备数
    getMaxDevices(licenseType) {
        const deviceMap = { dragon: 5, annual: 3, trial: 1 };
        return deviceMap[licenseType] || 1;
    }

    // 计算校验码
    calculateChecksum(data) {
        const hash = crypto.createHash('sha256').update(data).digest();
        return hash[0] ^ hash[1] ^ hash[2] ^ hash[3];
    }

    // 验证激活码格式和校验位
    validateActivationCode(code, licenseType) {
        if (!code || code.length !== 24) {
            return { valid: false, message: '激活码格式错误' };
        }

        // 检查字符集
        for (let char of code) {
            if (!this.charset.includes(char)) {
                return { valid: false, message: '激活码包含无效字符' };
            }
        }

        // 验证校验位
        const codeWithoutChecksum = code.slice(0, 20);
        const providedChecksum = code.slice(20);
        const calculatedChecksum = this.calculateChecksum(codeWithoutChecksum + licenseType);
        
        // 简化校验验证
        const expectedChecksum = Array.from({length: 4}, (_, i) => 
            this.charset[(calculatedChecksum + i) % this.charset.length]
        ).join('');

        if (providedChecksum !== expectedChecksum) {
            return { valid: false, message: '激活码校验失败' };
        }

        return { valid: true, message: '激活码格式正确' };
    }

    // 加密激活码列表
    encryptLicenseList(licenses) {
        try {
            const password = this.getEncryptionKey();
            const salt = crypto.randomBytes(16);
            const iv = crypto.randomBytes(16);
            const key = crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');

            // 准备数据
            const data = {
                version: '2.0',
                timestamp: Date.now(),
                count: licenses.length,
                licenses: licenses
            };

            // 压缩数据
            const jsonData = JSON.stringify(data);
            const compressed = zlib.gzipSync(jsonData);

            // 加密压缩后的数据
            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            let encrypted = cipher.update(compressed, null, 'base64');
            encrypted += cipher.final('base64');

            return {
                data: encrypted,
                salt: salt.toString('base64'),
                iv: iv.toString('base64'),
                algorithm: this.algorithm,
                compression: 'gzip',
                metadata: {
                    version: '2.0',
                    count: licenses.length,
                    timestamp: Date.now(),
                    checksum: this.generateChecksum(jsonData)
                }
            };
        } catch (error) {
            throw new Error(`激活码列表加密失败: ${error.message}`);
        }
    }

    // 解密激活码列表
    decryptLicenseList(encryptedData) {
        try {
            const password = this.getEncryptionKey();
            const salt = Buffer.from(encryptedData.salt, 'base64');
            const iv = Buffer.from(encryptedData.iv, 'base64');
            const key = crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');

            // 解密数据
            const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, iv);
            let decrypted = decipher.update(encryptedData.data, 'base64');
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            // 解压数据
            const decompressed = zlib.gunzipSync(decrypted);
            const jsonData = decompressed.toString('utf8');
            const data = JSON.parse(jsonData);

            // 验证校验和
            if (encryptedData.metadata && encryptedData.metadata.checksum) {
                const expectedChecksum = this.generateChecksum(jsonData);
                if (expectedChecksum !== encryptedData.metadata.checksum) {
                    throw new Error('数据校验失败，可能已被篡改');
                }
            }

            return data;
        } catch (error) {
            throw new Error(`激活码列表解密失败: ${error.message}`);
        }
    }

    // 获取加密密钥
    getEncryptionKey() {
        const config = require('./qiniu-config');
        const key = config.getRaw().encryptionKey;
        if (!key) {
            throw new Error('加密密钥未配置');
        }
        return key;
    }

    // 生成数据校验和
    generateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // 测试功能
    test() {
        console.log('🔐 === 思源笔记激活码系统测试 ===\n');

        // 测试激活码生成
        console.log('🎲 测试激活码生成...');
        const dragonLicense = this.generateLicense('dragon', 'user123', '恶龙会员', '测试生成');
        const annualLicense = this.generateLicense('annual', 'user456', '年付会员', '测试生成');
        const trialLicense = this.generateLicense('trial', 'user789', '体验会员', '测试生成');

        console.log(`恶龙会员: ${dragonLicense.code} (密钥: ${dragonLicense.key.substring(0, 8)}...)`);
        console.log(`年付会员: ${annualLicense.code} (密钥: ${annualLicense.key.substring(0, 8)}...)`);
        console.log(`体验会员: ${trialLicense.code} (密钥: ${trialLicense.key.substring(0, 8)}...)`);

        // 测试激活码解析
        console.log('\n📋 测试激活码解析...');
        try {
            const parsed = this.parseActivationCode(dragonLicense.code);
            console.log(`解析结果: 类型=${parsed.licenseType}, 时间=${parsed.timeDigits}`);
        } catch (error) {
            console.error('解析失败:', error.message);
        }

        // 测试激活码验证
        console.log('\n✅ 测试激活码验证...');
        const validation = this.validateActivationCode(dragonLicense.code, 'dragon');
        console.log(`验证结果: ${validation.valid ? '✅ 通过' : '❌ 失败'} - ${validation.message}`);

        console.log('\n🎯 测试完成！');
    }
}

// 导出单例实例
module.exports = new EncryptionManager();
