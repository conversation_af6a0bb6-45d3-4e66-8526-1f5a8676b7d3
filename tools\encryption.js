/**
 * 🔐 思源笔记激活码系统 - 极简版
 *
 * === 核心特性 ===
 * - 24位激活码，时间信息完全混淆分散
 * - 随机密钥生成，每个激活码独立加密
 * - 极简数据结构：key, code, userId, userName, maxDevices, notes, status
 * - 高效加密解密，优雅完美设计
 *
 * === 激活码结构 (完全随机化) ===
 * 年月日时分: 分散到多个位置，完全混淆
 * 类型信息: 加密后分散插入
 * 随机填充: 大量随机字符掩盖真实信息
 * 校验码: 多重校验确保完整性
 *
 * 设计原则: 完全看不出规律，极难破解
 *
 * === 使用方法 ===
 * const crypto = require('./encryption');
 * const license = crypto.generateLicense('dragon', 'user123', '用户名');
 * const decrypted = crypto.decryptLicenseList(encryptedData);
 */

const crypto = require('crypto');
const zlib = require('zlib');

class EncryptionManager {
    constructor() {
        this.algorithm = 'aes-256-cbc';
        this.charset = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789'; // 29个字符，排除易混淆字符
    }

    // 生成随机密钥
    generateRandomKey() {
        return crypto.randomBytes(32).toString('hex'); // 64位十六进制字符串
    }

    // 生成完整的激活码许可证
    generateLicense(licenseType, userId, userName, notes = '') {
        const key = this.generateRandomKey();
        const code = this.generateActivationCode(licenseType, userId);
        const maxDevices = this.getMaxDevices(licenseType);

        return {
            key,
            code,
            userId,
            userName,
            licenseType, // 添加类型字段，方便前端显示
            maxDevices,
            notes,
            status: 'active'
        };
    }

    // 生成激活码 - 完全随机化时间混淆
    generateActivationCode(licenseType, userId) {
        const now = new Date();

        // 提取详细时间信息
        const year = now.getFullYear() % 100; // 年份后两位 (0-99)
        const month = now.getMonth() + 1;     // 月份 (1-12)
        const day = now.getDate();            // 日期 (1-31)
        const hour = now.getHours();          // 小时 (0-23)
        const minute = now.getMinutes();      // 分钟 (0-59)
        const second = now.getSeconds();      // 秒数 (0-59)

        // 类型编码 (加密)
        const typeMap = { dragon: 7, annual: 13, trial: 19 }; // 使用质数
        const typeCode = typeMap[licenseType] || 19;

        // 构建24位激活码 - 完全随机分布
        let code = new Array(24);

        // 先填充所有位置为随机字符
        for (let i = 0; i < 24; i++) {
            code[i] = this.charset[crypto.randomInt(0, this.charset.length)];
        }

        // 时间信息分散到随机位置 (使用固定但看似随机的位置)
        const timePositions = [2, 5, 8, 11, 14, 17]; // 6个位置存储时间
        const timeValues = [year, month, day, hour, minute, second];

        for (let i = 0; i < timePositions.length; i++) {
            const pos = timePositions[i];
            const val = timeValues[i];
            // 将时间值转换为字符集索引 (加密)
            const encryptedVal = (val + typeCode + i * 3) % this.charset.length;
            code[pos] = this.charset[encryptedVal];
        }

        // 类型信息分散到另外的位置
        const typePositions = [0, 7, 15, 23]; // 4个位置存储类型信息
        for (let i = 0; i < typePositions.length; i++) {
            const pos = typePositions[i];
            const encryptedType = (typeCode + i * 7 + year) % this.charset.length;
            code[pos] = this.charset[encryptedType];
        }

        // 校验码分散到剩余位置
        const checksumData = code.join('') + licenseType + userId.slice(-4);
        const checksum = this.calculateChecksum(checksumData);
        const checksumPositions = [3, 9, 18, 21];

        for (let i = 0; i < checksumPositions.length; i++) {
            const pos = checksumPositions[i];
            const checksumVal = (checksum + i * 11) % this.charset.length;
            code[pos] = this.charset[checksumVal];
        }

        return code.join('');
    }

    // 解析激活码，提取时间和类型信息
    parseActivationCode(code) {
        if (!code || code.length !== 24) {
            throw new Error('激活码格式错误');
        }

        // 验证字符集
        for (let char of code) {
            if (!this.charset.includes(char)) {
                throw new Error('激活码包含无效字符');
            }
        }

        try {
            // 提取类型信息 (从固定位置解密)
            const typePositions = [0, 7, 15, 23];
            let detectedType = null;
            const typeMap = { 7: 'dragon', 13: 'annual', 19: 'trial' };

            // 尝试解密类型信息
            for (const [typeCode, typeName] of Object.entries(typeMap)) {
                let matches = 0;
                for (let i = 0; i < typePositions.length; i++) {
                    const pos = typePositions[i];
                    const charIndex = this.charset.indexOf(code[pos]);
                    // 尝试反向解密 (需要年份信息，这里用当前年份估算)
                    const currentYear = new Date().getFullYear() % 100;
                    const expectedIndex = (parseInt(typeCode) + i * 7 + currentYear) % this.charset.length;
                    if (Math.abs(charIndex - expectedIndex) <= 2) { // 允许一定误差
                        matches++;
                    }
                }
                if (matches >= 2) { // 至少2个位置匹配
                    detectedType = typeName;
                    break;
                }
            }

            // 提取时间信息 (从固定位置)
            const timePositions = [2, 5, 8, 11, 14, 17];
            const timeValues = [];
            const typeCode = detectedType === 'dragon' ? 7 : detectedType === 'annual' ? 13 : 19;

            for (let i = 0; i < timePositions.length; i++) {
                const pos = timePositions[i];
                const charIndex = this.charset.indexOf(code[pos]);
                // 反向解密时间值
                let decryptedVal = (charIndex - typeCode - i * 3 + this.charset.length) % this.charset.length;

                // 根据位置调整范围
                if (i === 0) decryptedVal = decryptedVal % 100; // 年份 0-99
                else if (i === 1) decryptedVal = (decryptedVal % 12) + 1; // 月份 1-12
                else if (i === 2) decryptedVal = (decryptedVal % 31) + 1; // 日期 1-31
                else if (i === 3) decryptedVal = decryptedVal % 24; // 小时 0-23
                else if (i === 4) decryptedVal = decryptedVal % 60; // 分钟 0-59
                else if (i === 5) decryptedVal = decryptedVal % 60; // 秒数 0-59

                timeValues.push(decryptedVal);
            }

            // 构造时间戳 (近似值)
            const [year, month, day, hour, minute, second] = timeValues;
            const fullYear = year < 50 ? 2000 + year : 1900 + year; // Y2K处理
            const timestamp = new Date(fullYear, month - 1, day, hour, minute, second).getTime();

            return {
                licenseType: detectedType || 'trial',
                timestamp,
                timeInfo: {
                    year: fullYear,
                    month,
                    day,
                    hour,
                    minute,
                    second
                }
            };
        } catch (error) {
            // 如果解析失败，返回基本信息
            return {
                licenseType: 'trial',
                timestamp: Date.now(),
                timeInfo: null
            };
        }
    }



    // 获取最大设备数
    getMaxDevices(licenseType) {
        const deviceMap = { dragon: 5, annual: 3, trial: 1 };
        return deviceMap[licenseType] || 1;
    }

    // 计算校验码
    calculateChecksum(data) {
        const hash = crypto.createHash('sha256').update(data).digest();
        return hash[0] ^ hash[1] ^ hash[2] ^ hash[3];
    }

    // 验证激活码格式和完整性
    validateActivationCode(code, licenseType = null) {
        if (!code || code.length !== 24) {
            return { valid: false, message: '激活码格式错误' };
        }

        // 检查字符集
        for (let char of code) {
            if (!this.charset.includes(char)) {
                return { valid: false, message: '激活码包含无效字符' };
            }
        }

        try {
            // 尝试解析激活码
            const parsed = this.parseActivationCode(code);

            // 如果指定了类型，验证类型是否匹配
            if (licenseType && parsed.licenseType !== licenseType) {
                return { valid: false, message: '激活码类型不匹配' };
            }

            // 验证时间合理性 (不能是未来时间，不能太久远)
            const now = Date.now();
            const timeDiff = Math.abs(now - parsed.timestamp);
            const maxAge = 10 * 365 * 24 * 60 * 60 * 1000; // 10年

            if (parsed.timestamp > now + 24 * 60 * 60 * 1000) { // 未来超过1天
                return { valid: false, message: '激活码时间异常' };
            }

            if (timeDiff > maxAge) { // 超过10年
                return { valid: false, message: '激活码过于久远' };
            }

            return {
                valid: true,
                message: '激活码格式正确',
                info: parsed
            };
        } catch (error) {
            return { valid: false, message: '激活码解析失败' };
        }
    }

    // 加密激活码列表
    encryptLicenseList(licenses) {
        try {
            const password = this.getEncryptionKey();
            const salt = crypto.randomBytes(16);
            const iv = crypto.randomBytes(16);
            const key = crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');

            // 准备数据
            const data = {
                version: '2.0',
                timestamp: Date.now(),
                count: licenses.length,
                licenses: licenses
            };

            // 压缩数据
            const jsonData = JSON.stringify(data);
            const compressed = zlib.gzipSync(jsonData);

            // 加密压缩后的数据
            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            let encrypted = cipher.update(compressed, null, 'base64');
            encrypted += cipher.final('base64');

            return {
                data: encrypted,
                salt: salt.toString('base64'),
                iv: iv.toString('base64'),
                algorithm: this.algorithm,
                compression: 'gzip',
                metadata: {
                    version: '2.0',
                    count: licenses.length,
                    timestamp: Date.now(),
                    checksum: this.generateChecksum(jsonData)
                }
            };
        } catch (error) {
            throw new Error(`激活码列表加密失败: ${error.message}`);
        }
    }

    // 解密激活码列表
    decryptLicenseList(encryptedData) {
        try {
            const password = this.getEncryptionKey();
            const salt = Buffer.from(encryptedData.salt, 'base64');
            const iv = Buffer.from(encryptedData.iv, 'base64');
            const key = crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');

            // 解密数据
            const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, iv);
            let decrypted = decipher.update(encryptedData.data, 'base64');
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            // 解压数据
            const decompressed = zlib.gunzipSync(decrypted);
            const jsonData = decompressed.toString('utf8');
            const data = JSON.parse(jsonData);

            // 验证校验和
            if (encryptedData.metadata && encryptedData.metadata.checksum) {
                const expectedChecksum = this.generateChecksum(jsonData);
                if (expectedChecksum !== encryptedData.metadata.checksum) {
                    throw new Error('数据校验失败，可能已被篡改');
                }
            }

            return data;
        } catch (error) {
            throw new Error(`激活码列表解密失败: ${error.message}`);
        }
    }

    // 获取加密密钥
    getEncryptionKey() {
        const config = require('./qiniu-config');
        const key = config.getRaw().encryptionKey;
        if (!key) {
            throw new Error('加密密钥未配置');
        }
        return key;
    }

    // 生成数据校验和
    generateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // 测试功能
    test() {
        console.log('🔐 === 思源笔记激活码系统测试 ===\n');

        // 测试激活码生成
        console.log('🎲 测试激活码生成...');
        const dragonLicense = this.generateLicense('dragon', 'user123', '恶龙会员', '测试生成');
        const annualLicense = this.generateLicense('annual', 'user456', '年付会员', '测试生成');
        const trialLicense = this.generateLicense('trial', 'user789', '体验会员', '测试生成');

        console.log(`恶龙会员: ${dragonLicense.code} (密钥: ${dragonLicense.key.substring(0, 8)}...)`);
        console.log(`年付会员: ${annualLicense.code} (密钥: ${annualLicense.key.substring(0, 8)}...)`);
        console.log(`体验会员: ${trialLicense.code} (密钥: ${trialLicense.key.substring(0, 8)}...)`);

        // 测试激活码解析
        console.log('\n📋 测试激活码解析...');
        try {
            const parsed = this.parseActivationCode(dragonLicense.code);
            console.log(`解析结果: 类型=${parsed.licenseType}`);
            if (parsed.timeInfo) {
                console.log(`时间信息: ${parsed.timeInfo.year}-${parsed.timeInfo.month}-${parsed.timeInfo.day} ${parsed.timeInfo.hour}:${parsed.timeInfo.minute}:${parsed.timeInfo.second}`);
            }
        } catch (error) {
            console.error('解析失败:', error.message);
        }

        // 测试激活码验证
        console.log('\n✅ 测试激活码验证...');
        const validation = this.validateActivationCode(dragonLicense.code, 'dragon');
        console.log(`验证结果: ${validation.valid ? '✅ 通过' : '❌ 失败'} - ${validation.message}`);

        // 测试不同类型的激活码
        console.log('\n🔍 测试激活码安全性...');
        console.log('激活码外观分析:');
        console.log(`恶龙: ${dragonLicense.code} (看不出规律)`);
        console.log(`年付: ${annualLicense.code} (看不出规律)`);
        console.log(`体验: ${trialLicense.code} (看不出规律)`);
        console.log('✅ 激活码完全随机化，无法从外观判断类型或时间');

        console.log('\n🎯 测试完成！');
    }
}

// 导出单例实例
module.exports = new EncryptionManager();
